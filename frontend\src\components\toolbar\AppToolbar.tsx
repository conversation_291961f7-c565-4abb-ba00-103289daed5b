import React, { useState } from "react";
import { Button, ButtonGroup } from "@blueprintjs/core";
import { Fa<PERSON>og, <PERSON>aEye, FaEyeSlash, FaHome } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

interface AppToolbarProps {
  showAnnotationControls?: boolean;
}

const AppToolbar: React.FC<AppToolbarProps> = ({ showAnnotationControls = false }) => {
  const navigate = useNavigate();
  const [isMainToolbarVisible, setIsMainToolbarVisible] = useState<boolean>(true);
  const [isAnnotationPropertiesVisible, setIsAnnotationPropertiesVisible] =
    useState<boolean>(false);

  const handleToggleMainToolbar = () => {
    setIsMainToolbarVisible(!isMainToolbarVisible);
    // Dispatch custom event to notify viewers
    window.dispatchEvent(
      new CustomEvent("toggleMainToolbar", {
        detail: { visible: !isMainToolbarVisible },
      })
    );
  };

  const handleShowAnnotationProperties = () => {
    setIsAnnotationPropertiesVisible(!isAnnotationPropertiesVisible);
    // Dispatch custom event to notify viewers
    window.dispatchEvent(
      new CustomEvent("toggleAnnotationProperties", {
        detail: { visible: !isAnnotationPropertiesVisible },
      })
    );
  };

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <div className="app-toolbar">
      <div className="app-toolbar-content">
        <div className="app-toolbar-left">
          <Button icon={<FaHome />} onClick={handleGoHome} />
        </div>

        {showAnnotationControls && (
          <div className="app-toolbar-right">
            <ButtonGroup>
              <Button
                icon={<FaCog />}
                onClick={handleShowAnnotationProperties}
                active={isAnnotationPropertiesVisible}
              />
              <Button
                icon={isMainToolbarVisible ? <FaEyeSlash /> : <FaEye />}
                onClick={handleToggleMainToolbar}
              />
            </ButtonGroup>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppToolbar;
