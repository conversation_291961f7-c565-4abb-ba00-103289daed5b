import React from "react";
import type { ToolDefinition, ToolGridProps } from "@/models";
import { toolDefinitions } from "@/lib/fabric/tools";

/*
Handles different tool types (mode, action, toggle) consistently.
*/
const createToolClickHandler = (
  onToolSelect: (mode: any) => void,
  onCrop: (() => void) | undefined,
  onRotate: (() => void) | undefined,
  onFlipHorizontal: (() => void) | undefined,
  onFlipVertical: (() => void) | undefined,
  onGrayscaleChange: ((value: boolean) => void) | undefined,
  onInvertChange: ((value: boolean) => void) | undefined,

  cropData: any,
  grayscale: boolean,
  invert: boolean,
  disableGrayscale: boolean
) => {
  return async (tool: ToolDefinition) => {
    if (tool.type === "mode") {
      if (tool.mode === "crop") {
        if (cropData.isCropped) {
          onCrop?.();
          onToolSelect("select");
        } else {
          onToolSelect("crop");
        }
        return;
      }
      if (tool.mode) onToolSelect(tool.mode);
    } else if (tool.type === "action") {
      switch (tool.action) {
        case "rotate":
          onRotate?.();
          break;
        case "flipHorizontal":
          onFlipHorizontal?.();
          break;
        case "flipVertical":
          onFlipVertical?.();
          break;
      }
    } else if (tool.type === "toggle") {
      switch (tool.action) {
        case "grayscale":
          if (!disableGrayscale) {
            onGrayscaleChange?.(!grayscale);
          }
          break;
        case "invert":
          onInvertChange?.(!invert);
          break;
      }
    }
  };
};

const ToolGrid: React.FC<ToolGridProps> = ({
  activeMode,
  cropData,
  onToolSelect,
  onCrop,
  grayscale,
  invert,
  disableGrayscale,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
}) => {
  const handleToolClick = createToolClickHandler(
    onToolSelect,
    onCrop,
    onRotate,
    onFlipHorizontal,
    onFlipVertical,
    onGrayscaleChange,
    onInvertChange,
    cropData,
    grayscale || false,
    invert || false,
    disableGrayscale || false
  );

  return (
    <div className="tool-grid">
      {toolDefinitions.map((tool, index) => {
        const IconComponent = tool.icon;

        let isActive = false;
        let isDisabled = false;
        let buttonTitle = tool.title;

        if (tool.type === "mode") {
          isActive = activeMode === tool.mode;
          if (tool.mode === "crop" && cropData.isCropped) {
            buttonTitle = "Restore original image";
          }
        } else if (tool.type === "toggle") {
          if (tool.action === "grayscale") {
            isActive = grayscale || false;
            isDisabled = disableGrayscale || false;
          } else if (tool.action === "invert") {
            isActive = invert || false;
          }
        }

        return (
          <button
            key={tool.mode || tool.action || index}
            className={`tool-btn ${isActive ? "active" : ""} ${isDisabled ? "disabled" : ""}`}
            onClick={() => handleToolClick(tool)}
            disabled={isDisabled}
            title={buttonTitle}
          >
            <IconComponent />
          </button>
        );
      })}
    </div>
  );
};

export default ToolGrid;
