import React from "react";
import { FaUndo, FaSave } from "react-icons/fa";
import type { ActionButtonsProps } from "@/models";

const ActionButtons: React.FC<ActionButtonsProps> = ({ canUndo, onUndo, onSave }) => {
  return (
    <>
      {onUndo && (
        <button
          className={`undo-btn ${!canUndo ? "disabled" : ""}`}
          onClick={canUndo ? onUndo : undefined}
          disabled={!canUndo}
          title="Undo"
        >
          <FaUndo />
        </button>
      )}

      <button className="save-btn" onClick={onSave} title="Save">
        <FaSave />
      </button>
    </>
  );
};

export default ActionButtons;
