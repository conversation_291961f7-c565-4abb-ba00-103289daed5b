import React from "react";
import { <PERSON><PERSON>, <PERSON>lide<PERSON> } from "@blueprintjs/core";
import { Canvas } from "fabric";
import type { ToolMode } from "@/models";
import {
  getObjectColor,
  getObjectStrokeWidth,
  getObjectFontSize,
  getObjectFontWeight,
  getObjectFontStyle,
  getObjectUnderline,
  getObjectFontFamily,
  getObjectLineStyle,
  applyStrokeWidthContextAware,
  applyColorContextAware,
  applyFontSizeContextAware,
  applyFontWeightContextAware,
  applyFontStyleContextAware,
  applyUnderlineContextAware,
  applyFontFamilyContextAware,
  applyLineStyleContextAware,
} from "@/lib/fabric/operations";

type BottomToolbarMode = "properties" | "magnifier";

/*
Unified properties toolbar that shows different controls based on current tool.
Integrates magnifier settings and annotation properties in sleek interface.
*/
interface BottomToolbarProps {
  activeMode: BottomToolbarMode | null;
  onClose: () => void;
  fabricCanvas?: React.RefObject<Canvas | null>;
  currentTool?: ToolMode | null;
  magnifierZoom?: number;
  onMagnifierZoomChange?: (zoom: number) => void;
  magnifierRadius?: number;
  onMagnifierRadiusChange?: (radius: number) => void;
  fontSize?: number;
  onFontSizeChange?: (size: number) => void;
  isBold?: boolean;
  onBoldToggle?: () => void;
  isItalic?: boolean;
  onItalicToggle?: () => void;
  isUnderline?: boolean;
  onUnderlineToggle?: () => void;
  strokeWidth?: number;
  onStrokeWidthChange?: (width: number) => void;
  annotationColor?: string;
  onAnnotationColorChange?: (color: string) => void;
  fontFamily?: string;
  onFontFamilyChange?: (fontFamily: string) => void;
  lineStyle?: string;
  onLineStyleChange?: (lineStyle: string) => void;
}

/*
Determines which controls should be enabled based on current tool or selected object
*/
const getControlsState = (currentTool: ToolMode | null | undefined, selectedObject: any) => {
  if (selectedObject) {
    const objectType = selectedObject.type;
    const objectName = selectedObject.name || selectedObject.customType;

    return {
      showMagnifier: false,
      showStrokeWidth:
        objectType !== "textbox" && objectName !== "cropRect" && objectName !== "protractor",
      showColor: true,
      showFontSize: objectType === "textbox",
      showBold: objectType === "textbox",
      showItalic: objectType === "textbox",
      showUnderline: objectType === "textbox",
      showFontFamily: objectType === "textbox",
      showLineStyle:
        objectType !== "textbox" &&
        objectName !== "cropRect" &&
        objectName !== "protractor" &&
        objectName !== "highlight",
    };
  }

  // No object selected - show controls based on current tool
  switch (currentTool) {
    case "magnifier":
      return {
        showMagnifier: true,
        showStrokeWidth: false,
        showColor: false,
        showFontSize: false,
        showBold: false,
        showItalic: false,
        showUnderline: false,
        showFontFamily: false,
        showLineStyle: false,
      };
    case "text":
      return {
        showMagnifier: false,
        showStrokeWidth: false,
        showColor: true,
        showFontSize: true,
        showBold: true,
        showItalic: true,
        showUnderline: true,
        showFontFamily: true,
        showLineStyle: false,
      };
    case "highlight":
      return {
        showMagnifier: false,
        showStrokeWidth: false,
        showColor: false,
        showFontSize: false,
        showBold: false,
        showItalic: false,
        showUnderline: false,
        showFontFamily: false,
        showLineStyle: false,
      };
    case "freehand":
    case "line":
    case "arrow":
    case "rect":
    case "circle":
    case "measure":
      return {
        showMagnifier: false,
        showStrokeWidth: true,
        showColor: true,
        showFontSize: false,
        showBold: false,
        showItalic: false,
        showUnderline: false,
        showFontFamily: false,
        showLineStyle: true,
      };
    default:
      return {
        showMagnifier: false,
        showStrokeWidth: false,
        showColor: false,
        showFontSize: false,
        showBold: false,
        showItalic: false,
        showUnderline: false,
        showFontFamily: false,
        showLineStyle: false,
      };
  }
};

const BottomToolbar: React.FC<BottomToolbarProps> = ({
  activeMode,
  onClose,
  fabricCanvas,
  currentTool,
  magnifierZoom,
  onMagnifierZoomChange,
  magnifierRadius,
  onMagnifierRadiusChange,
  fontSize,
  onFontSizeChange,
  isBold,
  onBoldToggle,
  isItalic,
  onItalicToggle,
  isUnderline,
  onUnderlineToggle,
  strokeWidth,
  onStrokeWidthChange,
  annotationColor,
  onAnnotationColorChange,
  fontFamily,
  onFontFamilyChange,
  lineStyle,
  onLineStyleChange,
}) => {
  // Get selected object from canvas
  const selectedObject = fabricCanvas?.current?.getActiveObject();

  // Force re-render when object properties change
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);

  // Determine which controls to show based on current tool or selected object
  const controlsState = getControlsState(currentTool, selectedObject);

  // Get current values from selected object or fall back to hook state (already initialized from session storage)
  const getCurrentStrokeWidth = () => {
    const objectStrokeWidth = getObjectStrokeWidth(selectedObject);
    return objectStrokeWidth !== undefined ? objectStrokeWidth : strokeWidth;
  };

  const getCurrentColor = () => {
    const objectColor = getObjectColor(selectedObject);
    return objectColor !== undefined ? objectColor : annotationColor;
  };

  const getCurrentFontSize = () => {
    const objectFontSize = getObjectFontSize(selectedObject);
    return objectFontSize !== undefined ? objectFontSize : fontSize;
  };

  const getCurrentBold = () => {
    const objectBold = getObjectFontWeight(selectedObject);
    return objectBold !== undefined ? objectBold : isBold;
  };

  const getCurrentItalic = () => {
    const objectItalic = getObjectFontStyle(selectedObject);
    return objectItalic !== undefined ? objectItalic : isItalic;
  };

  const getCurrentUnderline = () => {
    const objectUnderline = getObjectUnderline(selectedObject);
    return objectUnderline !== undefined ? objectUnderline : isUnderline;
  };

  const getCurrentFontFamily = () => {
    const objectFontFamily = getObjectFontFamily(selectedObject);
    return objectFontFamily !== undefined ? objectFontFamily : fontFamily;
  };

  const getCurrentLineStyle = () => {
    const objectLineStyle = getObjectLineStyle(selectedObject);
    return objectLineStyle !== undefined ? objectLineStyle : lineStyle;
  };

  /*
  Context-aware property change handlers.
  Delegate to operation files following established code patterns.
  */
  const handleStrokeWidthChange = (width: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    applyStrokeWidthContextAware(canvas, width, onStrokeWidthChange);

    // Force re-render to update slider position when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleColorChange = (newColor: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    applyColorContextAware(canvas, newColor, onAnnotationColorChange);

    // Force re-render to update color picker when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleFontSizeChange = (size: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    applyFontSizeContextAware(canvas, size, onFontSizeChange);

    // Force re-render to update slider position when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleBoldToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    const newBold = !getCurrentBold();
    applyFontWeightContextAware(canvas, newBold, onBoldToggle);

    // Force re-render to update button state when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleItalicToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    const newItalic = !getCurrentItalic();
    applyFontStyleContextAware(canvas, newItalic, onItalicToggle);

    // Force re-render to update button state when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleUnderlineToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    const newUnderline = !getCurrentUnderline();
    applyUnderlineContextAware(canvas, newUnderline, onUnderlineToggle);

    // Force re-render to update button when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleFontFamilyChange = (newFontFamily: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    applyFontFamilyContextAware(canvas, newFontFamily, onFontFamilyChange);

    // Force re-render to update dropdown when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  const handleLineStyleChange = (newLineStyle: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    applyLineStyleContextAware(canvas, newLineStyle, onLineStyleChange);

    // Force re-render to update dropdown when selected object changes
    if (selectedObject) {
      forceUpdate();
    }
  };

  if (!activeMode || activeMode !== "properties") {
    return null;
  }

  const renderUnifiedPropertiesControls = () => (
    <div className="bottom-toolbar-content">
      {controlsState.showMagnifier && (
        <>
          <div className="toolbar-section">
            <span className="toolbar-label">Zoom:</span>
            <div className="toolbar-slider">
              <Slider
                min={1}
                max={5}
                stepSize={0.1}
                value={magnifierZoom}
                onChange={onMagnifierZoomChange}
                labelRenderer={false}
                showTrackFill={false}
              />
            </div>
          </div>
          <div className="toolbar-section">
            <span className="toolbar-label">Size:</span>
            <div className="toolbar-slider">
              <Slider
                min={20}
                max={300}
                stepSize={1}
                value={magnifierRadius}
                labelRenderer={false}
                onChange={onMagnifierRadiusChange}
                showTrackFill={false}
              />
            </div>
          </div>
        </>
      )}
      {controlsState.showFontSize && (
        <div className="toolbar-section">
          <span className="toolbar-label">Font Size</span>
          <div className="toolbar-slider">
            <Slider
              min={8}
              max={72}
              stepSize={1}
              value={getCurrentFontSize()}
              onChange={handleFontSizeChange}
              labelRenderer={false}
              showTrackFill={false}
            />
          </div>
        </div>
      )}
      {controlsState.showBold && (
        <div className="toolbar-section">
          <Button icon="bold" active={getCurrentBold()} onClick={handleBoldToggle} minimal />
        </div>
      )}
      {controlsState.showItalic && (
        <div className="toolbar-section">
          <Button icon="italic" active={getCurrentItalic()} onClick={handleItalicToggle} minimal />
        </div>
      )}
      {controlsState.showUnderline && (
        <div className="toolbar-section">
          <Button
            icon="underline"
            active={getCurrentUnderline()}
            onClick={handleUnderlineToggle}
            minimal
          />
        </div>
      )}
      {controlsState.showFontFamily && (
        <div className="toolbar-section">
          <span className="toolbar-label">Font</span>
          <select
            value={getCurrentFontFamily()}
            onChange={(e) => handleFontFamilyChange(e.target.value)}
            className="toolbar-font-select"
          >
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Calibri">Calibri</option>
          </select>
        </div>
      )}
      {controlsState.showStrokeWidth && (
        <div className="toolbar-section">
          <span className="toolbar-label">Stroke</span>
          <div className="toolbar-slider">
            <Slider
              min={1}
              max={10}
              stepSize={1}
              value={getCurrentStrokeWidth()}
              onChange={handleStrokeWidthChange}
              labelRenderer={false}
              showTrackFill={false}
            />
          </div>
        </div>
      )}
      {controlsState.showLineStyle && (
        <div className="toolbar-section">
          <span className="toolbar-label">Line</span>
          <select
            value={getCurrentLineStyle()}
            onChange={(e) => handleLineStyleChange(e.target.value)}
            className="toolbar-font-select"
          >
            <option value="solid">Solid</option>
            <option value="dashed">Dashed</option>
            <option value="dotted">Dotted</option>
          </select>
        </div>
      )}
      {controlsState.showColor && (
        <div className="toolbar-section">
          <span className="toolbar-label">Color</span>
          <div className="toolbar-color-wrapper">
            <input
              type="color"
              value={getCurrentColor()}
              onChange={(e) => handleColorChange(e.target.value)}
              className="toolbar-color-picker"
            />
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="bottom-toolbar">
      <Button
        icon="cross"
        fill={false}
        size="small"
        onClick={onClose}
        className="toolbar-close-button"
      />
      {renderUnifiedPropertiesControls()}
    </div>
  );
};

export default BottomToolbar;
