import { useState, useCallback, useEffect } from "react";
import { Canvas } from "fabric";
import {
  updateMagnifier<PERSON>oom as updateCanvasMagnifierZoom,
  updateMagnifierRadius as updateCanvasMagnifierRadius,
} from "@/lib/fabric/operations/magnifier";
import { getAllDefaults } from "@/lib/fabric/utils";

type BottomToolbarMode = "properties" | "magnifier";

/*
Manages bottom toolbar state and object property detection.
Handles unified interface for magnifier settings and annotation properties.
*/
interface BottomToolbarState {
  magnifierZoom: number;
  magnifierRadius: number;
  fontSize: number;
  isBold: boolean;
  isItalic: boolean;
  isUnderline: boolean;
  strokeWidth: number;
  annotationColor: string;
  fontFamily: string;
  lineStyle: string;
}

const initialState: BottomToolbarState = getAllDefaults();

export const useBottomToolbar = (fabricCanvas?: React.RefObject<Canvas | null>) => {
  const [state, setState] = useState<BottomToolbarState>(initialState);
  const [isVisible, setIsVisible] = useState(false);
  const [activeMode, setActiveMode] = useState<BottomToolbarMode | null>(null);
  const [selectedObject, setSelectedObject] = useState<any>(null);

  // Helper function to show toolbar when object is selected
  const updateToolbarForSelectedObject = useCallback((obj: any) => {
    if (!obj) return;

    setActiveMode("properties");
  }, []);

  // Apply text settings to canvas when they change
  useEffect(() => {
    if (fabricCanvas?.current && activeMode === "properties") {
      const canvas = fabricCanvas.current;
      const annotationColor = (canvas as any).annotationColor || "#ff0000";
      (canvas as any).textSettings = {
        fontSize: state.fontSize,
        fontWeight: state.isBold ? "bold" : "normal",
        fontStyle: state.isItalic ? "italic" : "normal",
        fill: annotationColor,
      };
    }
  }, [fabricCanvas, activeMode, state.fontSize, state.isBold, state.isItalic]);

  // Apply annotation settings to canvas for new annotations
  useEffect(() => {
    if (fabricCanvas?.current && activeMode === "properties") {
      const canvas = fabricCanvas.current;
      (canvas as any).annotationSettings = {
        strokeWidth: state.strokeWidth,
      };

      if (canvas.freeDrawingBrush) {
        canvas.freeDrawingBrush.width = state.strokeWidth;
      }
    }
  }, [fabricCanvas, activeMode, state.strokeWidth]);

  /*
  Registers Fabric selection event handlers for toolbar property detection.
  Updates toolbar content when objects are selected if toolbar is visible.
  */
  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;

    const handleSelectionCreated = (e: any) => {
      const selectedObj = e.selected?.[0];
      if (selectedObj) {
        setSelectedObject(selectedObj);
        if (isVisible) {
          updateToolbarForSelectedObject(selectedObj);
        }
      }
    };

    const handleSelectionUpdated = (e: any) => {
      handleSelectionCreated(e);
    };

    const handleSelectionCleared = () => {
      setSelectedObject(null);
    };

    const disposers: (() => void)[] = [];

    disposers.push(canvas.on("selection:created", handleSelectionCreated));
    disposers.push(canvas.on("selection:updated", handleSelectionUpdated));
    disposers.push(canvas.on("selection:cleared", handleSelectionCleared));

    return () => {
      disposers.forEach((dispose) => dispose());
    };
  }, [fabricCanvas, isVisible, updateToolbarForSelectedObject]);

  const showToolbar = useCallback((mode: BottomToolbarMode) => {
    setActiveMode(mode);
    setIsVisible(true);
  }, []);

  const hideToolbar = useCallback(() => {
    setIsVisible(false);
    setActiveMode(null);
  }, []);
  // Simple state updaters for session storage defaults

  const updateMagnifierZoom = useCallback(
    (zoom: number) => {
      setState((prev) => ({ ...prev, magnifierZoom: zoom }));
      if (fabricCanvas?.current) {
        updateCanvasMagnifierZoom(fabricCanvas.current, zoom);
      }
    },
    [fabricCanvas]
  );

  const updateMagnifierRadius = useCallback(
    (radius: number) => {
      setState((prev) => ({ ...prev, magnifierRadius: radius }));
      if (fabricCanvas?.current) {
        updateCanvasMagnifierRadius(fabricCanvas.current, radius);
      }
    },
    [fabricCanvas]
  );

  const updateFontSize = useCallback((size: number) => {
    setState((prev) => ({ ...prev, fontSize: size }));
  }, []);

  const toggleBold = useCallback(() => {
    setState((prev) => ({ ...prev, isBold: !prev.isBold }));
  }, []);

  const toggleItalic = useCallback(() => {
    setState((prev) => ({ ...prev, isItalic: !prev.isItalic }));
  }, []);

  const toggleUnderline = useCallback(() => {
    setState((prev) => ({ ...prev, isUnderline: !prev.isUnderline }));
  }, []);

  const updateStrokeWidth = useCallback((width: number) => {
    setState((prev) => ({ ...prev, strokeWidth: width }));
  }, []);

  const updateAnnotationColor = useCallback((color: string) => {
    setState((prev) => ({ ...prev, annotationColor: color }));
  }, []);

  const updateFontFamily = useCallback((fontFamily: string) => {
    setState((prev) => ({ ...prev, fontFamily }));
  }, []);

  const updateLineStyle = useCallback((lineStyle: string) => {
    setState((prev) => ({ ...prev, lineStyle }));
  }, []);

  const toggleAnnotationProperties = useCallback(() => {
    if (isVisible) {
      setIsVisible(false);
      setActiveMode(null);
    } else {
      if (selectedObject) {
        updateToolbarForSelectedObject(selectedObject);
      } else {
        setActiveMode("properties");
      }
      setIsVisible(true);
    }
  }, [isVisible, selectedObject, updateToolbarForSelectedObject]);

  return {
    isVisible,
    activeMode,
    ...state,
    showToolbar,
    hideToolbar,
    toggleAnnotationProperties,
    updateMagnifierZoom,
    updateMagnifierRadius,
    updateFontSize,
    toggleBold,
    toggleItalic,
    toggleUnderline,
    updateStrokeWidth,
    updateAnnotationColor,
    updateFontFamily,
    updateLineStyle,
  };
};
