import React, { useState, useEffect, useCallback } from "react";
import type { ImageToolbarProps, CalibrationData } from "@/models";
import { useFabricTools } from "@/hooks/useFabricTools";
import { useBottomToolbar } from "@/hooks/useBottomToolbar";

import { Rnd } from "react-rnd";
import {
  createCalibrationSubmitHandler,
  createCalibrationCloseHandler,
  getCalibrationFromLocalStorage,
  clearCalibrationFromLocalStorage,
} from "@/lib/fabric/operations";
import { FaGripVertical, FaArrowsAlt } from "react-icons/fa";
import ActionButtons from "./ActionButtons";
import SliderControls from "./AdjustmentControls";
import CalibrationModal from "./CalibrationModal";
import CalibrationPrompt from "./CalibrationPrompt";

import GammaControls from "./GammaControls";
import ToolGrid from "./ToolGrid";
import BottomToolbar from "./BottomToolbar";

import {
  setAnnotationColorDefault,
  setStrokeWidthDefault,
  setFontSizeDefault,
  setBoldDefault,
  setItalicDefault,
  setUnderlineDefault,
  setFontFamilyDefault,
  setMagnifierZoomDefault,
  setMagnifierRadiusDefault,
  setLineStyleDefault,
} from "@/lib/fabric/utils";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  fabricConfigs,
  handlers,
  state,
  config = {},
  onShapeCreated,
}) => {
  const { disableGrayscale = false, disableGamma = false } = config;
  const [isCalibrationModalOpen, setCalibrationModalOpen] = useState(false);
  const [isCalibrationPromptOpen, setCalibrationPromptOpen] = useState(false);
  const [localCalibrationData, setLocalCalibrationData] = useState<CalibrationData>(() => {
    const localStorageData = getCalibrationFromLocalStorage();
    return localStorageData ?? fabricConfigs.calibrationData!;
  });
  useEffect(() => {
    if (fabricConfigs.calibrationData) {
      setLocalCalibrationData(fabricConfigs.calibrationData);
    }
    return () => {
      clearCalibrationFromLocalStorage();
    };
  }, [fabricConfigs.calibrationData]);

  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    cropData: state.cropData,
    onShapeCreated,
    onCrop: handlers.actions.handleCrop,
    disableUndoTracking: handlers.tracking.disableUndoTracking,
    enableUndoTracking: handlers.tracking.enableUndoTracking,
    showCalibrationModal: () => setCalibrationModalOpen(true),
    calibrationData: localCalibrationData || undefined,
    onCalibrationPrompt: () => setCalibrationPromptOpen(true),
  });

  // State for toolbar visibility controlled by AppToolbar
  const [isMainToolbarVisible, setIsMainToolbarVisible] = useState<boolean>(true);

  const bottomToolbar = useBottomToolbar(fabricCanvas);

  // Create wrapper functions that update both hook state and session storage
  const handleFontSizeChange = useCallback(
    (size: number) => {
      bottomToolbar.updateFontSize(size);
      setFontSizeDefault(size);
    },
    [bottomToolbar]
  );

  const handleBoldToggle = useCallback(() => {
    bottomToolbar.toggleBold();
    setBoldDefault(!bottomToolbar.isBold);
  }, [bottomToolbar]);

  const handleItalicToggle = useCallback(() => {
    bottomToolbar.toggleItalic();
    setItalicDefault(!bottomToolbar.isItalic);
  }, [bottomToolbar]);

  const handleUnderlineToggle = useCallback(() => {
    bottomToolbar.toggleUnderline();
    setUnderlineDefault(!bottomToolbar.isUnderline);
  }, [bottomToolbar]);

  const handleStrokeWidthChange = useCallback(
    (width: number) => {
      bottomToolbar.updateStrokeWidth(width);
      setStrokeWidthDefault(width);
    },
    [bottomToolbar]
  );

  const handleAnnotationColorChange = useCallback(
    (color: string) => {
      bottomToolbar.updateAnnotationColor(color);
      setAnnotationColorDefault(color);
    },
    [bottomToolbar]
  );

  const handleFontFamilyChange = useCallback(
    (fontFamily: string) => {
      bottomToolbar.updateFontFamily(fontFamily);
      setFontFamilyDefault(fontFamily);
    },
    [bottomToolbar]
  );

  const handleMagnifierZoomChange = useCallback(
    (zoom: number) => {
      bottomToolbar.updateMagnifierZoom(zoom);
      setMagnifierZoomDefault(zoom);
    },
    [bottomToolbar]
  );

  const handleMagnifierRadiusChange = useCallback(
    (radius: number) => {
      bottomToolbar.updateMagnifierRadius(radius);
      setMagnifierRadiusDefault(radius);
    },
    [bottomToolbar]
  );

  const handleLineStyleChange = useCallback(
    (lineStyle: string) => {
      bottomToolbar.updateLineStyle(lineStyle);
      setLineStyleDefault(lineStyle);
    },
    [bottomToolbar]
  );

  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    (canvas as any).annotationColor = bottomToolbar.annotationColor;
  }, [bottomToolbar.annotationColor, fabricCanvas]);

  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    (canvas as any).annotationSettings = {
      ...(canvas as any).annotationSettings,
      strokeWidth: bottomToolbar.strokeWidth,
      lineStyle: bottomToolbar.lineStyle,
    };
  }, [bottomToolbar.strokeWidth, bottomToolbar.lineStyle, fabricCanvas]);

  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    (canvas as any).textSettings = {
      ...(canvas as any).textSettings,
      fontSize: bottomToolbar.fontSize,
      fontWeight: bottomToolbar.isBold ? "bold" : "normal",
      fontStyle: bottomToolbar.isItalic ? "italic" : "normal",
      underline: bottomToolbar.isUnderline,
      fontFamily: bottomToolbar.fontFamily,
      fill: bottomToolbar.annotationColor,
    };
  }, [
    bottomToolbar.fontSize,
    bottomToolbar.isBold,
    bottomToolbar.isItalic,
    bottomToolbar.isUnderline,
    bottomToolbar.fontFamily,
    bottomToolbar.annotationColor,
    fabricCanvas,
  ]);

  // Listen for events from AppToolbar
  useEffect(() => {
    const handleToggleMainToolbar = (event: CustomEvent) => {
      setIsMainToolbarVisible(event.detail.visible);
    };

    const handleToggleAnnotationProperties = (event: CustomEvent) => {
      if (event.detail.visible) {
        bottomToolbar.showToolbar("properties");
      } else {
        bottomToolbar.hideToolbar();
      }
    };

    window.addEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
    window.addEventListener(
      "toggleAnnotationProperties",
      handleToggleAnnotationProperties as EventListener
    );

    return () => {
      window.removeEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
      window.removeEventListener(
        "toggleAnnotationProperties",
        handleToggleAnnotationProperties as EventListener
      );
    };
  }, [bottomToolbar]);

  return (
    <>
      {isMainToolbarVisible && (
        <Rnd
          default={{
            x: 0,
            y: window.innerHeight / 2 - 150,
            width: 175,
            height: 340,
          }}
          bounds=".viewer-panel"
          enableResizing={false}
          dragHandleClassName="drag-handle"
        >
          <div className="fabric-toolbar-vertical">
            <div className="drag-handle">
              <FaGripVertical className="drag-icon" />
              <span className="drag-text">Tools</span>
              <FaArrowsAlt className="drag-icon" />
            </div>
            <div className="annotation-tools">
              <ToolGrid
                activeMode={activeMode}
                cropData={state.cropData}
                onToolSelect={(mode) => {
                  changeToolMode(mode);
                  if (mode === "magnifier") {
                    bottomToolbar.showToolbar("properties");
                  } else {
                    // Don't auto-hide toolbar for other tools
                  }
                }}
                onCrop={handlers.actions.handleCrop}
                grayscale={fabricConfigs.grayscale}
                invert={fabricConfigs.invert}
                disableGrayscale={disableGrayscale}
                onRotate={handlers.transform.handleRotate}
                onFlipHorizontal={handlers.transform.handleFlipHorizontal}
                onFlipVertical={handlers.transform.handleFlipVertical}
                onGrayscaleChange={handlers.filter.handleGrayscaleChange}
                onInvertChange={handlers.filter.handleInvertChange}
              />
            </div>
            <SliderControls
              brightness={fabricConfigs.brightness}
              contrast={fabricConfigs.contrast}
              sharpness={fabricConfigs.sharpness}
              onBrightnessChange={handlers.filter.handleBrightnessChange}
              onContrastChange={handlers.filter.handleContrastChange}
              onSharpnessChange={handlers.filter.handleSharpnessChange}
            />

            <GammaControls
              gammaR={fabricConfigs.gammaR}
              gammaG={fabricConfigs.gammaG}
              gammaB={fabricConfigs.gammaB}
              disableGamma={disableGamma}
              onGammaRChange={handlers.filter.handleGammaRChange}
              onGammaGChange={handlers.filter.handleGammaGChange}
              onGammaBChange={handlers.filter.handleGammaBChange}
            />
            <ActionButtons
              canUndo={state.canUndo}
              onUndo={handlers.actions.handleUndo}
              onSave={handlers.actions.handleSave}
            />
          </div>
        </Rnd>
      )}
      <CalibrationModal
        isOpen={isCalibrationModalOpen}
        onClose={createCalibrationCloseHandler(
          fabricCanvas?.current,
          setCalibrationModalOpen,
          handlers.tracking.disableUndoTracking,
          handlers.tracking.enableUndoTracking
        )}
        onSubmit={createCalibrationSubmitHandler(
          fabricCanvas?.current,
          () => {
            const newCalibrationData = getCalibrationFromLocalStorage();
            setLocalCalibrationData(newCalibrationData ?? fabricConfigs.calibrationData!);
            setCalibrationModalOpen(false);
            changeToolMode("measure");
          },
          handlers.tracking.disableUndoTracking,
          handlers.tracking.enableUndoTracking
        )}
      />
      <CalibrationPrompt
        isOpen={isCalibrationPromptOpen}
        onClose={() => setCalibrationPromptOpen(false)}
        onCalibrate={() => {
          setCalibrationPromptOpen(false);
          changeToolMode("calibrate");
        }}
      />
      {bottomToolbar.isVisible && (
        <BottomToolbar
          activeMode={bottomToolbar.activeMode}
          onClose={bottomToolbar.hideToolbar}
          fabricCanvas={fabricCanvas}
          currentTool={activeMode}
          // Magnifier props
          magnifierZoom={bottomToolbar.magnifierZoom}
          onMagnifierZoomChange={handleMagnifierZoomChange}
          magnifierRadius={bottomToolbar.magnifierRadius}
          onMagnifierRadiusChange={handleMagnifierRadiusChange}
          // Text props
          fontSize={bottomToolbar.fontSize}
          onFontSizeChange={handleFontSizeChange}
          isBold={bottomToolbar.isBold}
          onBoldToggle={handleBoldToggle}
          isItalic={bottomToolbar.isItalic}
          onItalicToggle={handleItalicToggle}
          isUnderline={bottomToolbar.isUnderline}
          onUnderlineToggle={handleUnderlineToggle}
          fontFamily={bottomToolbar.fontFamily}
          onFontFamilyChange={handleFontFamilyChange}
          // Shape/Annotation props
          strokeWidth={bottomToolbar.strokeWidth}
          onStrokeWidthChange={handleStrokeWidthChange}
          lineStyle={bottomToolbar.lineStyle}
          onLineStyleChange={handleLineStyleChange}
          // Unified annotation color
          annotationColor={bottomToolbar.annotationColor}
          onAnnotationColorChange={handleAnnotationColorChange}
        />
      )}
    </>
  );
};

export default FabricToolbar;
