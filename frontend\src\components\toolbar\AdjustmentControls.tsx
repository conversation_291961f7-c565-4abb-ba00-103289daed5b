import React from "react";
import { FaSun, <PERSON>a<PERSON>dju<PERSON>, FaSearchPlus, FaUndo } from "react-icons/fa";
import type { SliderControlsProps } from "@/models";

const SliderControls: React.FC<SliderControlsProps> = ({
  brightness,
  contrast,
  sharpness,
  onBrightnessChange,
  onContrastChange,
  onSharpnessChange,
}) => {
  const sliders = [
    {
      icon: FaSun,
      label: "Bright",
      value: brightness,
      onChange: onBrightnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      default: 1,
    },
    {
      icon: FaAdjust,
      label: "Contrast",
      value: contrast,
      onChange: onContrastChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      default: 1,
    },
    {
      icon: FaSearchPlus,
      label: "Sharpness",
      value: sharpness,
      onChange: onSharpnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
      default: 1,
    },
  ];

  return (
    <>
      {sliders.map((slider) => {
        const IconComponent = slider.icon;
        return (
          <div key={slider.label} className="control-item">
            <div className="control-icon" title={slider.label}>
              <IconComponent />
            </div>
            <input
              type="range"
              min={slider.min}
              max={slider.max}
              step={slider.step}
              value={slider.value}
              onChange={(e) => slider.onChange(parseFloat(e.target.value))}
              className="horizontal-slider"
              title={`${slider.label}: ${(slider.value || 0).toFixed(1)}`}
            />
            <span>{(slider.value || 0).toFixed(1)}</span>
            <button
              type="button"
              className="reset-slider-btn"
              title={`Reset ${slider.label}`}
              onClick={() => slider.onChange(slider.default)}
            >
              <FaUndo className="control-icon" />
            </button>
          </div>
        );
      })}
    </>
  );
};

export default SliderControls;
